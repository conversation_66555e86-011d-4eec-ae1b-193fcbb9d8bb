.\" Man page generated from reStructuredText.
.
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.TH "PIPENV" "1" "May 29, 2025" "2025.0.3" "pipenv"
.sp
This guide provides comprehensive instructions for installing Pipenv on various platforms and environments. Follow the approach that best suits your system and requirements.
.SH PREREQUISITES
.sp
Before installing Pipenv, ensure you have Python and pip available on your system.
.SS Verifying Python Installation
.sp
Check that Python is installed and available from your command line:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ python \-\-version
Python 3.10.4

.EE
.UNINDENT
.UNINDENT
.sp
You should see output showing your Python version. If you don’t have Python installed, download and install the latest version from \X'tty: link https://python.org'\fI\%python.org\fP\X'tty: link'\&.
.SS Verifying pip Installation
.sp
Ensure pip is available:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pip \-\-version
pip 22.1.2

.EE
.UNINDENT
.UNINDENT
.sp
If pip is not installed, you can install it following the \X'tty: link https://pip.pypa.io/en/stable/installation/'\fI\%pip installation guide\fP\X'tty: link'\&.
.SH INSTALLATION METHODS
.SS Recommended: User Installation
.sp
The recommended approach is to install Pipenv for your user account. This avoids permission issues and prevents conflicts with system packages:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pip install \-\-user pipenv

.EE
.UNINDENT
.UNINDENT
.sp
This installs Pipenv in your user site\-packages directory.
.SS Adding Pipenv to PATH
.sp
After installing with \fB\-\-user\fP, you may need to add the user site\-packages binary directory to your PATH.
.SS On Linux and macOS
.sp
Find the user base binary directory:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ python \-m site \-\-user\-base
/home/<USER>/.local

.EE
.UNINDENT
.UNINDENT
.sp
Add the \fBbin\fP directory to your PATH by adding this line to your shell configuration file (e.g., \fB~/.bashrc\fP, \fB~/.zshrc\fP, or \fB~/.profile\fP):
.INDENT 0.0
.INDENT 3.5
.sp
.EX
export PATH=\(dq$HOME/.local/bin:$PATH\(dq

.EE
.UNINDENT
.UNINDENT
.sp
Then reload your shell configuration:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ source ~/.bashrc  # or ~/.zshrc, ~/.profile, etc.

.EE
.UNINDENT
.UNINDENT
.SS On Windows
.sp
Find the user site\-packages directory:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
> python \-m site \-\-user\-site
C:\eUsers\eUsername\eAppData\eRoaming\ePython\ePython310\esite\-packages

.EE
.UNINDENT
.UNINDENT
.sp
Replace \fBsite\-packages\fP with \fBScripts\fP in the path, and add it to your PATH environment variable:
.INDENT 0.0
.IP 1. 3
Press \fBWin + X\fP and select “System”
.IP 2. 3
Click “Advanced system settings”
.IP 3. 3
Click “Environment Variables”
.IP 4. 3
Under “User variables”, select “Path” and click “Edit”
.IP 5. 3
Add the path (e.g., \fBC:\eUsers\eUsername\eAppData\eRoaming\ePython\ePython310\eScripts\fP)
.IP 6. 3
Click “OK” to save changes
.UNINDENT
.sp
You may need to restart your terminal or computer for the PATH changes to take effect.
.SS Alternative: System\-Wide Installation
.sp
If you have administrator privileges and want to install Pipenv system\-wide:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
# On Linux/macOS
$ sudo pip install pipenv

# On Windows (in an Administrator command prompt)
> pip install pipenv

.EE
.UNINDENT
.UNINDENT
.sp
\fBWARNING:\fP
.INDENT 0.0
.INDENT 3.5
System\-wide installation is not recommended for most users as it can lead to conflicts with your system package manager.
.UNINDENT
.UNINDENT
.SS Using Package Managers
.SS macOS with Homebrew
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ brew install pipenv

.EE
.UNINDENT
.UNINDENT
.sp
\fBNOTE:\fP
.INDENT 0.0
.INDENT 3.5
Homebrew installation is discouraged because it works better to install pipenv using pip on macOS.
.UNINDENT
.UNINDENT
.SS Debian/Ubuntu
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ sudo apt update
$ sudo apt install pipenv

.EE
.UNINDENT
.UNINDENT
.SS Fedora
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ sudo dnf install pipenv

.EE
.UNINDENT
.UNINDENT
.SS FreeBSD
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pkg install py39\-pipenv

.EE
.UNINDENT
.UNINDENT
.SS Gentoo
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ sudo emerge pipenv

.EE
.UNINDENT
.UNINDENT
.SS Void Linux
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ sudo xbps\-install \-S python3\-pipenv

.EE
.UNINDENT
.UNINDENT
.SS Using pipx
.sp
\X'tty: link https://pypa.github.io/pipx/'\fI\%pipx\fP\X'tty: link' is a tool to install and run Python applications in isolated environments:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
# Install pipx
$ pip install \-\-user pipx
$ python \-m pipx ensurepath

# Install Pipenv using pipx
$ pipx install pipenv

.EE
.UNINDENT
.UNINDENT
.sp
This is a good alternative to the \fB\-\-user\fP installation method, especially if you use multiple Python command\-line tools.
.SS Using Python Module
.sp
You can also run Pipenv as a Python module:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ python \-m pip install pipenv
$ python \-m pipenv

.EE
.UNINDENT
.UNINDENT
.sp
This approach is useful when you have multiple Python versions installed and want to ensure you’re using a specific one.
.SH VERIFYING INSTALLATION
.sp
After installation, verify that Pipenv is working correctly:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pipenv \-\-version
pipenv, version 2022.5.2

.EE
.UNINDENT
.UNINDENT
.sp
If you see the version number, Pipenv is installed correctly.
.SH UPGRADING PIPENV
.sp
To upgrade an existing Pipenv installation:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
# User installation
$ pip install \-\-user \-\-upgrade pipenv

# System\-wide installation
$ sudo pip install \-\-upgrade pipenv

# Homebrew
$ brew upgrade pipenv

# pipx
$ pipx upgrade pipenv

.EE
.UNINDENT
.UNINDENT
.SH INSTALLING SPECIFIC VERSIONS
.sp
If you need a specific version of Pipenv:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ pip install \-\-user pipenv==2022.1.8

.EE
.UNINDENT
.UNINDENT
.SH INSTALLATION IN VIRTUAL ENVIRONMENTS
.sp
You can install Pipenv inside a virtual environment, although this is less common:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
$ python \-m venv pipenv\-venv
$ source pipenv\-venv/bin/activate  # On Windows: pipenv\-venv\eScripts\eactivate
(pipenv\-venv) $ pip install pipenv

.EE
.UNINDENT
.UNINDENT
.SH DOCKER INSTALLATION
.sp
For Docker environments, you can install Pipenv in your Dockerfile:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
FROM python:3.10\-slim

# Install pipenv
RUN pip install pipenv

# Set working directory
WORKDIR /app

# Copy Pipfile and Pipfile.lock
COPY Pipfile Pipfile.lock ./

# Install dependencies
RUN pipenv install \-\-system \-\-deploy

# Copy application code
COPY . .

# Run the application
CMD [\(dqpython\(dq, \(dqapp.py\(dq]

.EE
.UNINDENT
.UNINDENT
.SH CI/CD INSTALLATION
.sp
For continuous integration environments:
.INDENT 0.0
.INDENT 3.5
.sp
.EX
# GitHub Actions example
name: Python CI

on: [push, pull_request]

jobs:
  build:
    runs\-on: ubuntu\-latest

    steps:
    \- uses: actions/checkout@v3
    \- name: Set up Python
      uses: actions/setup\-python@v4
      with:
        python\-version: \(aq3.10\(aq
    \- name: Install pipenv
      run: |
        python \-m pip install \-\-upgrade pip
        pip install pipenv
    \- name: Install dependencies
      run: |
        pipenv install \-\-dev
    \- name: Run tests
      run: |
        pipenv run pytest

.EE
.UNINDENT
.UNINDENT
.SH TROUBLESHOOTING
.SS Command Not Found
.sp
If you get a “command not found” error after installation:
.INDENT 0.0
.IP 1. 3
Check if Pipenv is installed in your user site\-packages:
.INDENT 3.0
.INDENT 3.5
.sp
.EX
$ python \-m pipenv \-\-version

.EE
.UNINDENT
.UNINDENT
.IP 2. 3
If that works, add the user site\-packages bin directory to your PATH as described above.
.IP 3. 3
Try restarting your terminal or computer.
.UNINDENT
.SS Permission Errors
.sp
If you encounter permission errors during installation:
.INDENT 0.0
.IP 1. 3
Use the \fB\-\-user\fP flag to install in your home directory:
.INDENT 3.0
.INDENT 3.5
.sp
.EX
$ pip install \-\-user pipenv

.EE
.UNINDENT
.UNINDENT
.IP 2. 3
If using sudo, ensure you’re using it correctly:
.INDENT 3.0
.INDENT 3.5
.sp
.EX
$ sudo pip install pipenv

.EE
.UNINDENT
.UNINDENT
.IP 3. 3
Check file permissions in your installation directories.
.UNINDENT
.SS Python Version Compatibility
.sp
Pipenv requires Python 3.7 or newer. If you’re using an older version, you’ll need to upgrade Python first.
.SS pip Not Found
.sp
If pip is not found:
.INDENT 0.0
.IP 1. 3
Install pip:
.INDENT 3.0
.INDENT 3.5
.sp
.EX
# Download get\-pip.py
$ curl https://bootstrap.pypa.io/get\-pip.py \-o get\-pip.py

# Install pip
$ python get\-pip.py \-\-user

.EE
.UNINDENT
.UNINDENT
.IP 2. 3
Ensure pip is in your PATH.
.UNINDENT
.SH BEST PRACTICES
.INDENT 0.0
.IP 1. 3
\fBUse user installation\fP (\fB\-\-user\fP) to avoid permission issues and system conflicts.
.IP 2. 3
\fBKeep Pipenv updated\fP to benefit from the latest features and bug fixes.
.IP 3. 3
\fBConsider pipx\fP for a cleaner, isolated installation if you use multiple Python command\-line tools.
.IP 4. 3
\fBAdd Pipenv to your project’s development setup instructions\fP to ensure all developers use the same environment.
.IP 5. 3
\fBUse version control\fP for your \fBPipfile\fP and \fBPipfile.lock\fP to ensure consistent environments across your team.
.UNINDENT
.SH NEXT STEPS
.sp
Now that you have Pipenv installed, you can:
.INDENT 0.0
.IP 1. 3
Create a new project: \fBpipenv \-\-python 3.10\fP
.IP 2. 3
Install packages: \fBpipenv install requests\fP
.IP 3. 3
Activate the environment: \fBpipenv shell\fP
.IP 4. 3
Run commands: \fBpipenv run python script.py\fP
.UNINDENT
.sp
For more detailed usage instructions, see the \fI\%Quick Start Guide\fP and \fI\%Commands Reference\fP\&.
.SH AUTHOR
Python Packaging Authority
.SH COPYRIGHT
2020. A project founded by Kenneth Reitz and maintained by <a href="https://www.pypa.io/en/latest/">Python Packaging Authority (PyPA).</a>
.\" Generated by docutils manpage writer.
.
